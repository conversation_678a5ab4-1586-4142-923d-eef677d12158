<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Happy Birthday Priya! 🎂</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Comic Sans MS', cursive, sans-serif;
            background: linear-gradient(45deg, #ffb3d9, #ffe6f2, #fff0f8, #ffcceb);
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite;
            overflow: hidden;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .container {
            text-align: center;
            position: relative;
            z-index: 10;
        }

        .birthday-title {
            font-size: 4rem;
            color: #ff69b4;
            text-shadow: 3px 3px 6px rgba(255, 105, 180, 0.3);
            margin-bottom: 20px;
            animation: bounce 2s infinite;
        }

        .name {
            font-size: 5rem;
            color: #ff1493;
            text-shadow: 4px 4px 8px rgba(255, 20, 147, 0.4);
            margin: 20px 0;
            animation: sparkle 3s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-20px); }
            60% { transform: translateY(-10px); }
        }

        @keyframes sparkle {
            0%, 100% { transform: scale(1) rotate(0deg); }
            25% { transform: scale(1.1) rotate(2deg); }
            50% { transform: scale(1.05) rotate(-1deg); }
            75% { transform: scale(1.1) rotate(1deg); }
        }

        .cake {
            font-size: 6rem;
            margin: 30px 0;
            animation: cakeGlow 2s ease-in-out infinite alternate;
        }

        @keyframes cakeGlow {
            from { 
                filter: drop-shadow(0 0 10px #ffb3d9);
                transform: scale(1);
            }
            to { 
                filter: drop-shadow(0 0 20px #ff69b4);
                transform: scale(1.1);
            }
        }

        .balloons {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .balloon {
            position: absolute;
            font-size: 3rem;
            animation: float 6s ease-in-out infinite;
        }

        .balloon:nth-child(1) { left: 10%; animation-delay: 0s; }
        .balloon:nth-child(2) { left: 20%; animation-delay: 1s; }
        .balloon:nth-child(3) { right: 20%; animation-delay: 2s; }
        .balloon:nth-child(4) { right: 10%; animation-delay: 3s; }
        .balloon:nth-child(5) { left: 50%; animation-delay: 4s; }

        @keyframes float {
            0%, 100% { transform: translateY(100vh) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(10deg); }
        }

        .hearts {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .heart {
            position: absolute;
            color: #ff69b4;
            font-size: 2rem;
            animation: heartFloat 8s linear infinite;
        }

        @keyframes heartFloat {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        .message {
            font-size: 1.8rem;
            color: #d63384;
            margin-top: 30px;
            animation: fadeInOut 4s ease-in-out infinite;
            max-width: 600px;
            line-height: 1.4;
        }

        @keyframes fadeInOut {
            0%, 100% { opacity: 0.7; }
            50% { opacity: 1; }
        }

        .stars {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .star {
            position: absolute;
            color: #ffd700;
            font-size: 1.5rem;
            animation: twinkle 3s ease-in-out infinite;
        }

        @keyframes twinkle {
            0%, 100% { opacity: 0.3; transform: scale(0.8); }
            50% { opacity: 1; transform: scale(1.2); }
        }

        .click-message {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            color: #ff69b4;
            font-size: 1.2rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="birthday-title">Happy Birthday</div>
        <div class="name">Priya!</div>
        <div class="cake">🎂</div>
        <div class="message">
            May your special day be filled with happiness, laughter, and all the sweetest moments! 
            You deserve all the joy in the world! 🌟💕
        </div>
    </div>

    <div class="balloons">
        <div class="balloon">🎈</div>
        <div class="balloon">🎈</div>
        <div class="balloon">🎈</div>
        <div class="balloon">🎈</div>
        <div class="balloon">🎈</div>
    </div>

    <div class="hearts">
        <div class="heart">💖</div>
        <div class="heart">💕</div>
        <div class="heart">💗</div>
        <div class="heart">💝</div>
        <div class="heart">💖</div>
    </div>

    <div class="stars">
        <div class="star">⭐</div>
        <div class="star">✨</div>
        <div class="star">⭐</div>
        <div class="star">✨</div>
        <div class="star">⭐</div>
    </div>

    <div class="click-message">Click anywhere for a surprise! ✨</div>

    <script>
        // Add random positioning for floating elements
        function randomizePositions() {
            const hearts = document.querySelectorAll('.heart');
            const stars = document.querySelectorAll('.star');
            
            hearts.forEach((heart, index) => {
                heart.style.left = Math.random() * 90 + '%';
                heart.style.animationDelay = Math.random() * 8 + 's';
            });
            
            stars.forEach((star, index) => {
                star.style.left = Math.random() * 90 + '%';
                star.style.top = Math.random() * 90 + '%';
                star.style.animationDelay = Math.random() * 3 + 's';
            });
        }

        // Create confetti effect on click
        function createConfetti() {
            const colors = ['#ff69b4', '#ffb3d9', '#ffd700', '#ff1493', '#ffe6f2'];
            
            for (let i = 0; i < 50; i++) {
                const confetti = document.createElement('div');
                confetti.style.position = 'absolute';
                confetti.style.left = Math.random() * 100 + 'vw';
                confetti.style.top = '-10px';
                confetti.style.width = '10px';
                confetti.style.height = '10px';
                confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
                confetti.style.borderRadius = '50%';
                confetti.style.pointerEvents = 'none';
                confetti.style.animation = `confettiFall ${Math.random() * 3 + 2}s linear forwards`;
                
                document.body.appendChild(confetti);
                
                setTimeout(() => {
                    confetti.remove();
                }, 5000);
            }
        }

        // Add confetti animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes confettiFall {
                to {
                    transform: translateY(100vh) rotate(720deg);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // Event listeners
        document.addEventListener('click', createConfetti);
        
        // Initialize
        randomizePositions();
        
        // Refresh positions periodically
        setInterval(randomizePositions, 10000);
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Happy Birthday Priya! 🎂</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Comic Sans MS', cursive, sans-serif;
            background: linear-gradient(45deg, #9b59b6, #8e44ad, #663399, #4a148c, #6a1b9a, #9c27b0);
            background-size: 600% 600%;
            animation: gradientShift 10s ease infinite;
            overflow: hidden;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            25% { background-position: 100% 0%; }
            50% { background-position: 100% 100%; }
            75% { background-position: 0% 100%; }
            100% { background-position: 0% 50%; }
        }

        .container {
            text-align: center;
            position: relative;
            z-index: 10;
        }

        .birthday-title {
            font-size: 4rem;
            background: linear-gradient(45deg, #e91e63, #9c27b0, #673ab7, #3f51b5);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: textGradient 3s ease infinite, bounce 2s infinite;
            margin-bottom: 20px;
            filter: drop-shadow(0 0 10px rgba(156, 39, 176, 0.5));
        }

        .name {
            font-size: 5rem;
            background: linear-gradient(45deg, #8e24aa, #7b1fa2, #6a1b9a, #4a148c, #9c27b0);
            background-size: 400% 400%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: textGradient 2s ease infinite, nameFloat 4s ease-in-out infinite;
            margin: 20px 0;
            filter: drop-shadow(0 0 15px rgba(142, 36, 170, 0.7));
            position: relative;
        }

        @keyframes textGradient {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        @keyframes nameFloat {
            0%, 100% { transform: translateY(0px) scale(1); }
            25% { transform: translateY(-15px) scale(1.05); }
            50% { transform: translateY(-10px) scale(1.1); }
            75% { transform: translateY(-5px) scale(1.05); }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0) rotate(0deg); }
            40% { transform: translateY(-20px) rotate(2deg); }
            60% { transform: translateY(-10px) rotate(-1deg); }
        }

        .cake {
            font-size: 6rem;
            margin: 30px 0;
            animation: cakeGlow 2s ease-in-out infinite alternate, cakeRotate 8s linear infinite;
            position: relative;
        }

        @keyframes cakeGlow {
            from {
                filter: drop-shadow(0 0 15px #9c27b0) drop-shadow(0 0 30px #673ab7);
                transform: scale(1);
            }
            to {
                filter: drop-shadow(0 0 25px #8e24aa) drop-shadow(0 0 50px #4a148c);
                transform: scale(1.15);
            }
        }

        @keyframes cakeRotate {
            0% { transform: rotateY(0deg); }
            100% { transform: rotateY(360deg); }
        }

        .balloons {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .balloon {
            position: absolute;
            font-size: 3rem;
            animation: float 6s ease-in-out infinite;
            filter: drop-shadow(0 0 10px rgba(156, 39, 176, 0.6));
        }

        .balloon:nth-child(1) { left: 10%; animation-delay: 0s; color: #9c27b0; }
        .balloon:nth-child(2) { left: 20%; animation-delay: 1s; color: #673ab7; }
        .balloon:nth-child(3) { right: 20%; animation-delay: 2s; color: #8e24aa; }
        .balloon:nth-child(4) { right: 10%; animation-delay: 3s; color: #7b1fa2; }
        .balloon:nth-child(5) { left: 50%; animation-delay: 4s; color: #4a148c; }

        @keyframes float {
            0%, 100% { transform: translateY(100vh) rotate(0deg) scale(1); }
            25% { transform: translateY(50vh) rotate(90deg) scale(1.2); }
            50% { transform: translateY(-20px) rotate(180deg) scale(1.1); }
            75% { transform: translateY(25vh) rotate(270deg) scale(1.3); }
        }

        .hearts {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .heart {
            position: absolute;
            font-size: 2rem;
            animation: heartFloat 8s linear infinite;
            filter: drop-shadow(0 0 8px rgba(156, 39, 176, 0.8));
        }

        .heart:nth-child(1) { color: #e91e63; }
        .heart:nth-child(2) { color: #9c27b0; }
        .heart:nth-child(3) { color: #673ab7; }
        .heart:nth-child(4) { color: #8e24aa; }
        .heart:nth-child(5) { color: #7b1fa2; }

        @keyframes heartFloat {
            0% {
                transform: translateY(100vh) rotate(0deg) scale(0.5);
                opacity: 1;
            }
            50% {
                transform: translateY(50vh) rotate(180deg) scale(1.5);
                opacity: 0.8;
            }
            100% {
                transform: translateY(-100px) rotate(360deg) scale(0.8);
                opacity: 0;
            }
        }

        .message {
            font-size: 1.8rem;
            background: linear-gradient(45deg, #e1bee7, #ce93d8, #ba68c8, #ab47bc);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-top: 30px;
            animation: textGradient 3s ease infinite, messageGlow 4s ease-in-out infinite;
            max-width: 600px;
            line-height: 1.4;
            filter: drop-shadow(0 0 5px rgba(171, 71, 188, 0.4));
        }

        @keyframes messageGlow {
            0%, 100% {
                opacity: 0.8;
                transform: scale(1);
            }
            50% {
                opacity: 1;
                transform: scale(1.02);
            }
        }

        .stars {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .star {
            position: absolute;
            font-size: 1.5rem;
            animation: twinkle 3s ease-in-out infinite;
            filter: drop-shadow(0 0 10px currentColor);
        }

        .star:nth-child(1) { color: #e1bee7; }
        .star:nth-child(2) { color: #ce93d8; }
        .star:nth-child(3) { color: #ba68c8; }
        .star:nth-child(4) { color: #ab47bc; }
        .star:nth-child(5) { color: #9c27b0; }

        @keyframes twinkle {
            0%, 100% {
                opacity: 0.4;
                transform: scale(0.8) rotate(0deg);
            }
            50% {
                opacity: 1;
                transform: scale(1.5) rotate(180deg);
            }
        }

        /* Magical Particles */
        .magic-particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: radial-gradient(circle, #e1bee7, #9c27b0);
            border-radius: 50%;
            animation: particleFloat 12s linear infinite;
        }

        @keyframes particleFloat {
            0% {
                transform: translateY(100vh) translateX(0) scale(0);
                opacity: 0;
            }
            10% {
                opacity: 1;
                transform: translateY(90vh) translateX(20px) scale(1);
            }
            90% {
                opacity: 1;
                transform: translateY(10vh) translateX(-20px) scale(1);
            }
            100% {
                transform: translateY(-10vh) translateX(0) scale(0);
                opacity: 0;
            }
        }

        /* 3D Birthday Crown */
        .crown {
            position: absolute;
            top: 15%;
            left: 50%;
            transform: translateX(-50%);
            font-size: 4rem;
            animation: crownRotate 6s ease-in-out infinite;
            filter: drop-shadow(0 0 20px #9c27b0);
            z-index: 5;
        }

        @keyframes crownRotate {
            0%, 100% {
                transform: translateX(-50%) rotateY(0deg) scale(1);
            }
            25% {
                transform: translateX(-50%) rotateY(90deg) scale(1.2);
            }
            50% {
                transform: translateX(-50%) rotateY(180deg) scale(1.1);
            }
            75% {
                transform: translateX(-50%) rotateY(270deg) scale(1.3);
            }
        }

        .click-message {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(45deg, #e1bee7, #9c27b0, #673ab7);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.2rem;
            animation: textGradient 2s ease infinite, pulse 2s infinite;
            filter: drop-shadow(0 0 5px rgba(156, 39, 176, 0.5));
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 0.7;
                transform: translateX(-50%) scale(1);
            }
            50% {
                opacity: 1;
                transform: translateX(-50%) scale(1.05);
            }
        }
    </style>
</head>
<body>
    <div class="crown">👑</div>

    <div class="container">
        <div class="birthday-title">Happy Birthday</div>
        <div class="name">Priya!</div>
        <div class="cake">🎂</div>
        <div class="message">
            May your magical day sparkle with joy, laughter, and endless purple dreams!
            You're absolutely amazing and deserve all the happiness in the universe! ✨💜🦄
        </div>
    </div>

    <div class="balloons">
        <div class="balloon">🎈</div>
        <div class="balloon">🎈</div>
        <div class="balloon">🎈</div>
        <div class="balloon">🎈</div>
        <div class="balloon">🎈</div>
    </div>

    <div class="hearts">
        <div class="heart">💜</div>
        <div class="heart">💖</div>
        <div class="heart">💕</div>
        <div class="heart">🦄</div>
        <div class="heart">💜</div>
    </div>

    <div class="stars">
        <div class="star">⭐</div>
        <div class="star">✨</div>
        <div class="star">🌟</div>
        <div class="star">✨</div>
        <div class="star">⭐</div>
    </div>

    <div class="magic-particles">
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
    </div>

    <div class="click-message">Click anywhere for magical surprises! ✨🦄</div>

    <script>
        // Add random positioning for floating elements
        function randomizePositions() {
            const hearts = document.querySelectorAll('.heart');
            const stars = document.querySelectorAll('.star');
            const particles = document.querySelectorAll('.particle');

            hearts.forEach((heart, index) => {
                heart.style.left = Math.random() * 90 + '%';
                heart.style.animationDelay = Math.random() * 8 + 's';
            });

            stars.forEach((star, index) => {
                star.style.left = Math.random() * 90 + '%';
                star.style.top = Math.random() * 90 + '%';
                star.style.animationDelay = Math.random() * 3 + 's';
            });

            particles.forEach((particle, index) => {
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 12 + 's';
                particle.style.animationDuration = (8 + Math.random() * 8) + 's';
            });
        }

        // Create magical confetti effect on click
        function createConfetti() {
            const colors = ['#9c27b0', '#673ab7', '#8e24aa', '#7b1fa2', '#4a148c', '#e1bee7'];
            const shapes = ['💜', '✨', '🦄', '👑', '⭐', '💖'];

            for (let i = 0; i < 60; i++) {
                const confetti = document.createElement('div');
                confetti.style.position = 'absolute';
                confetti.style.left = Math.random() * 100 + 'vw';
                confetti.style.top = '-10px';

                if (Math.random() > 0.5) {
                    // Emoji confetti
                    confetti.textContent = shapes[Math.floor(Math.random() * shapes.length)];
                    confetti.style.fontSize = (Math.random() * 20 + 15) + 'px';
                } else {
                    // Geometric confetti
                    confetti.style.width = (Math.random() * 12 + 8) + 'px';
                    confetti.style.height = (Math.random() * 12 + 8) + 'px';
                    confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
                    confetti.style.borderRadius = Math.random() > 0.5 ? '50%' : '0';
                }

                confetti.style.pointerEvents = 'none';
                confetti.style.animation = `confettiFall ${Math.random() * 4 + 3}s linear forwards`;
                confetti.style.filter = 'drop-shadow(0 0 5px currentColor)';

                document.body.appendChild(confetti);

                setTimeout(() => {
                    confetti.remove();
                }, 7000);
            }
        }

        // Create magical burst effect
        function createMagicalBurst(x, y) {
            const burstColors = ['#e1bee7', '#ce93d8', '#ba68c8', '#ab47bc', '#9c27b0'];

            for (let i = 0; i < 20; i++) {
                const burst = document.createElement('div');
                burst.style.position = 'absolute';
                burst.style.left = x + 'px';
                burst.style.top = y + 'px';
                burst.style.width = '6px';
                burst.style.height = '6px';
                burst.style.backgroundColor = burstColors[Math.floor(Math.random() * burstColors.length)];
                burst.style.borderRadius = '50%';
                burst.style.pointerEvents = 'none';

                const angle = (i / 20) * Math.PI * 2;
                const distance = Math.random() * 100 + 50;
                const duration = Math.random() * 1 + 0.5;

                burst.style.animation = `burstOut ${duration}s ease-out forwards`;
                burst.style.setProperty('--angle', angle + 'rad');
                burst.style.setProperty('--distance', distance + 'px');

                document.body.appendChild(burst);

                setTimeout(() => {
                    burst.remove();
                }, duration * 1000);
            }
        }

        // Add enhanced animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes confettiFall {
                to {
                    transform: translateY(100vh) rotate(720deg) scale(0.5);
                    opacity: 0;
                }
            }
            @keyframes burstOut {
                to {
                    transform: translate(
                        calc(cos(var(--angle)) * var(--distance)),
                        calc(sin(var(--angle)) * var(--distance))
                    ) scale(0);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // Event listeners
        document.addEventListener('click', (e) => {
            createConfetti();
            createMagicalBurst(e.clientX, e.clientY);
        });
        
        // Initialize
        randomizePositions();
        
        // Refresh positions periodically
        setInterval(randomizePositions, 10000);
    </script>
</body>
</html>

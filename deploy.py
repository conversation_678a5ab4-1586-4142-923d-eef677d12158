#!/usr/bin/env python3
"""
Deploy <PERSON><PERSON>'s Birthday Animation to a free hosting service
"""
import os
import zipfile
import webbrowser
from pathlib import Path

def create_deployment_package():
    """Create a deployment package for the birthday animation"""
    
    # Create a zip file with all necessary files
    with zipfile.ZipFile('priya_birthday.zip', 'w') as zipf:
        zipf.write('index.html')
        if os.path.exists('README.md'):
            zipf.write('README.md')
    
    print("✅ Deployment package created: priya_birthday.zip")
    return True

def show_deployment_options():
    """Show different free hosting options"""
    
    print("\n🌐 FREE HOSTING OPTIONS FOR PRIYA'S BIRTHDAY ANIMATION:")
    print("=" * 60)
    
    print("\n🚀 OPTION 1: Netlify Drop (EASIEST - RECOMMENDED)")
    print("   1. Go to: https://app.netlify.com/drop")
    print("   2. Drag and drop the 'priya_birthday.zip' file")
    print("   3. Get instant shareable link!")
    print("   4. <PERSON> works worldwide on any device!")
    
    print("\n🚀 OPTION 2: Surge.sh (Command Line)")
    print("   1. Install: npm install -g surge")
    print("   2. Run: surge")
    print("   3. Follow prompts to deploy")
    
    print("\n🚀 OPTION 3: GitHub Pages")
    print("   1. Create GitHub repository")
    print("   2. Upload index.html")
    print("   3. Enable GitHub Pages")
    
    print("\n🚀 OPTION 4: Vercel")
    print("   1. Go to: https://vercel.com")
    print("   2. Drag and drop folder")
    print("   3. Get instant deployment")
    
    print("\n" + "=" * 60)
    print("🎯 RECOMMENDED: Use Netlify Drop - it's the fastest!")
    print("📱 The link will work on Priya's mobile anywhere in the world!")

def open_netlify():
    """Open Netlify Drop in browser"""
    webbrowser.open("https://app.netlify.com/drop")
    print("\n🌐 Opening Netlify Drop in your browser...")
    print("📁 Drag and drop the 'priya_birthday.zip' file to deploy!")

if __name__ == "__main__":
    print("🎂 DEPLOYING PRIYA'S BIRTHDAY ANIMATION")
    print("=" * 50)
    
    # Create deployment package
    create_deployment_package()
    
    # Show options
    show_deployment_options()
    
    # Ask user what they want to do
    print("\n" + "=" * 50)
    choice = input("🚀 Open Netlify Drop now? (y/n): ").lower().strip()
    
    if choice in ['y', 'yes']:
        open_netlify()
    else:
        print("\n✅ Deployment package ready!")
        print("📁 Upload 'priya_birthday.zip' to any hosting service above")
        print("🎉 Then share the link with Priya!")

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Share <PERSON><PERSON>'s Birthday Magic! 🎂</title>
    <style>
        body {
            font-family: 'Comic Sans MS', cursive, sans-serif;
            background: linear-gradient(45deg, #9b59b6, #8e44ad, #663399, #4a148c);
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite;
            color: white;
            text-align: center;
            padding: 50px 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .container {
            max-width: 600px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(156, 39, 176, 0.3);
        }

        h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .link-box {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 20px;
            margin: 30px 0;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .link {
            font-size: 1.5rem;
            font-weight: bold;
            color: #e1bee7;
            text-decoration: none;
            word-break: break-all;
        }

        .copy-btn {
            background: linear-gradient(45deg, #e91e63, #9c27b0);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.2rem;
            cursor: pointer;
            margin: 10px;
            transition: transform 0.3s ease;
            font-family: inherit;
        }

        .copy-btn:hover {
            transform: scale(1.05);
        }

        .instructions {
            font-size: 1.2rem;
            line-height: 1.6;
            margin: 20px 0;
        }

        .emoji {
            font-size: 2rem;
            margin: 0 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎂 Share Priya's Magical Birthday! 👑</h1>
        
        <div class="instructions">
            <span class="emoji">✨</span>
            Copy the link below and send it to Priya for an amazing birthday surprise!
            <span class="emoji">🦄</span>
        </div>

        <div class="link-box">
            <div class="link" id="shareLink">http://localhost:8000</div>
            <button class="copy-btn" onclick="copyLink()">📋 Copy Link</button>
        </div>

        <div class="instructions">
            <strong>💜 How to surprise Priya:</strong><br>
            1. Copy the link above<br>
            2. Send it via WhatsApp, text, or email<br>
            3. Tell her: "I have a magical birthday surprise for you! Click this link! 🎂✨"<br>
            4. Watch her face light up with joy! 😊
        </div>

        <div class="instructions">
            <span class="emoji">🎈</span>
            <strong>Pro tip:</strong> Tell her to click anywhere on the page for extra magic!
            <span class="emoji">⭐</span>
        </div>

        <button class="copy-btn" onclick="openAnimation()">🎂 Preview Animation</button>
    </div>

    <script>
        function copyLink() {
            const link = document.getElementById('shareLink').textContent;
            navigator.clipboard.writeText(link).then(() => {
                const btn = event.target;
                const originalText = btn.textContent;
                btn.textContent = '✅ Copied!';
                btn.style.background = 'linear-gradient(45deg, #4caf50, #8bc34a)';
                
                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.style.background = 'linear-gradient(45deg, #e91e63, #9c27b0)';
                }, 2000);
            });
        }

        function openAnimation() {
            window.open('http://localhost:8000', '_blank');
        }
    </script>
</body>
</html>
